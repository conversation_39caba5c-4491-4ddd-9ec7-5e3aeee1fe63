import { Head } from '@inertiajs/react';

interface Team {
    id: number;
    name: string;
}

interface Match {
    home_team: Team;
    away_team: Team;
    week: number;
}

interface FixturesProps {
    fixturesByWeek: Record<string, Match[]>;
}

export default function Fixtures({ fixturesByWeek }: FixturesProps) {
    const weekNumbers = Object.keys(fixturesByWeek).sort((a, b) => parseInt(a) - parseInt(b));
    const hasFixtures = weekNumbers.length > 0;

    const handleStartSimulation = () => {
        alert('Simulation started! (This is a placeholder)');
    };

    return (
        <>
            <Head title="Fixtures" />
            <div className="min-h-screen bg-gray-100 py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h1 className="text-2xl font-bold text-gray-900">
                                League Simulator - Fixtures
                            </h1>
                            <p className="mt-1 text-sm text-gray-600">
                                View weekly fixtures and start simulation
                            </p>
                        </div>

                        <div className="p-6">
                            {!hasFixtures ? (
                                <div className="text-center py-12">
                                    <div className="text-gray-400 mb-4">
                                        <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                                        No fixtures generated yet
                                    </h3>
                                    <p className="text-gray-500 mb-6">
                                        Go to teams page to generate fixtures
                                    </p>
                                    <a
                                        href="/teams"
                                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                                    >
                                        Go to Teams
                                    </a>
                                </div>
                            ) : (
                                <>
                                    <div className="mb-6 flex justify-between items-center">
                                        <div className="flex space-x-4">
                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {weekNumbers.length} weeks
                                            </span>
                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                {Object.values(fixturesByWeek).flat().length} matches
                                            </span>
                                        </div>
                                        
                                        <button
                                            onClick={handleStartSimulation}
                                            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
                                        >
                                            Start Simulation
                                        </button>
                                    </div>

                                    <div className="space-y-6">
                                        {weekNumbers.map((weekNumber) => (
                                            <div key={weekNumber} className="border border-gray-200 rounded-lg">
                                                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                                                    <h3 className="text-lg font-medium text-gray-900">
                                                        Week {weekNumber}
                                                    </h3>
                                                    <p className="text-sm text-gray-500">
                                                        {fixturesByWeek[weekNumber].length} matches
                                                    </p>
                                                </div>
                                                
                                                <div className="p-4">
                                                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                                        {fixturesByWeek[weekNumber].map((match, index) => (
                                                            <div
                                                                key={index}
                                                                className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
                                                            >
                                                                <div className="flex items-center justify-between">
                                                                    <div className="text-center flex-1">
                                                                        <div className="font-medium text-gray-900">
                                                                            {match.home_team.name}
                                                                        </div>
                                                                        <div className="text-xs text-gray-500">
                                                                            Home
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <div className="px-3">
                                                                        <span className="text-lg font-bold text-gray-400">
                                                                            VS
                                                                        </span>
                                                                    </div>
                                                                    
                                                                    <div className="text-center flex-1">
                                                                        <div className="font-medium text-gray-900">
                                                                            {match.away_team.name}
                                                                        </div>
                                                                        <div className="text-xs text-gray-500">
                                                                            Away
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </>
                            )}

                            <div className="mt-8">
                                <a
                                    href="/teams"
                                    className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
                                >
                                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                    </svg>
                                    Back to Teams
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
